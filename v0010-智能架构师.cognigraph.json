{"project_info": {"name": "v0.010智能架构师提示词系统设计", "description": "融合v0.004和v0.007双外脑系统核心优势，设计具备智能架构决策能力的新一代AI提示词系统，支持复杂架构设计、智能决策和专业架构师工作流程", "role": "AI提示词架构师", "created_date": "2025-01-28", "last_updated": "2025-01-28 10:30"}, "requirements": {"core_needs": ["融合v0.004企业架构4视图能力与v0.007精准执行机制", "设计智能架构师专业角色和决策引擎", "建立三外脑协同机制（CogniGraph + ArchGraph + ArchWisdom）", "保持Token效率的同时增强架构设计深度", "支持复杂架构决策和多方案智能对比分析"], "constraints": ["Token效率相比v0.004提升≥40%", "保持v0.007的执行精准度≥95%", "兼容现有双外脑文件格式和协同机制", "架构师专业能力覆盖度≥85%", "系统复杂度控制在中高等级（避免过度设计）"], "success_criteria": ["成功融合两版本核心优势≥90%", "智能架构决策准确率≥90%", "架构设计完整性和专业性≥85%", "用户体验和易用性≥90%", "系统可扩展性和可维护性≥85%"]}, "tasks": {"high_priority": ["提取v0.004和v0.007核心架构要素对比分析", "设计v0.010智能架构师核心框架和角色定位", "建立三外脑协同机制架构设计", "设计智能决策引擎和架构模式匹配系统", "定义架构师专业工作流程和方法论"], "medium_priority": ["设计架构质量评估和验证体系", "建立架构模式库和最佳实践知识库", "优化Token使用效率和执行性能", "设计多层次架构视图管理机制", "建立架构风险评估和决策支持系统"], "low_priority": ["完善系统文档和使用指南", "设计扩展机制和定制化能力", "建立测试验证和质量保证体系", "设计用户交互界面和体验优化", "建立持续改进和学习机制"]}, "decisions": {"key_decisions": ["采用三外脑架构：CogniGraph + ArchGraph + ArchWisdom（新增架构智慧库）", "保持v0.007的14阶段精准流程作为主干，在关键节点融入v0.004的4视图架构能力", "设计智能架构师角色，具备企业架构师专业知识和决策能力", "建立架构模式匹配引擎，支持自动化架构决策和方案对比"], "sequential_analysis": ["通过深度分析确定v0.010应该是融合创新而非简单叠加", "智能架构师定位为专业决策者而非单纯执行者", "三外脑机制能够有效平衡复杂度和效率"]}, "progress": {"completed": ["项目双外脑系统初始化完成 - 2025-01-28 10:30", "两版本核心架构要素提取完成 - 2025-01-28 10:30"], "in_progress": ["v0.010智能架构师核心框架设计 - 开始时间: 2025-01-28 10:30"], "pending": ["三外脑协同机制详细设计", "智能决策引擎架构设计", "架构师专业工作流程定义", "系统整体架构验证和优化"]}}