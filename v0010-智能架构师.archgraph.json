{"arch_info": {"project_name": "v0.010智能架构师提示词系统设计", "arch_version": "v1.0", "created_date": "2025-01-28", "last_updated": "2025-01-28 10:30"}, "core_architecture": {"modules": ["智能架构师引擎（核心决策和推理模块）", "三外脑协同管理器（CogniGraph + ArchGraph + ArchWisdom协调）", "架构模式匹配器（模式识别和推荐系统）", "多视图架构设计器（业务、应用、技术、数据4视图）", "精准执行流程引擎（14阶段流程管理）", "质量评估和验证器（多维质量检查系统）"], "dependencies": ["智能架构师引擎 → 三外脑协同管理器（决策结果同步）", "架构模式匹配器 → 智能架构师引擎（模式推荐输入）", "多视图架构设计器 → 三外脑协同管理器（架构设计存储）", "精准执行流程引擎 → 质量评估和验证器（执行结果验证）", "三外脑协同管理器 → 所有模块（状态同步和数据共享）"], "interfaces": ["架构师决策接口（支持复杂架构决策和方案对比）", "三外脑数据接口（CogniGraph、ArchGraph、ArchWisdom数据访问）", "架构模式查询接口（模式匹配和推荐服务）", "多视图设计接口（4视图架构设计和管理）", "流程执行接口（14阶段精准执行控制）", "质量评估接口（多维质量检查和验证）"], "data_flow": ["用户需求 → 智能架构师引擎 → 架构决策 → 三外脑协同管理器", "架构模式库 → 架构模式匹配器 → 推荐方案 → 智能架构师引擎", "架构设计 → 多视图架构设计器 → 4视图输出 → ArchGraph存储", "执行过程 → 精准执行流程引擎 → 进度状态 → CogniGraph更新", "质量数据 → 质量评估和验证器 → 质量报告 → 决策反馈"]}, "tech_stack": {"languages": ["Python 3.9+（主要开发语言，统一脚本编写）"], "frameworks": ["JSON Schema（数据结构验证和规范）", "Mermaid（架构图可视化生成）", "Sequential Thinking（复杂决策分析工具）"], "databases": ["JSON文件存储（轻量级数据持久化）", "架构模式库（结构化模式和最佳实践存储）"], "tools": ["Tavily（网络搜索和信息收集）", "Context7（技术文档和API参考）", "GitHub工具集（代码管理和协作）", "Playwright（自动化测试和验证）"]}, "deployment": {"environment": "AI助手集成环境（支持多工具调用和文件操作）", "structure": "模块化架构（松耦合、高内聚的组件设计）", "requirements": ["支持JSON文件读写和结构化数据处理", "支持多工具协同调用和状态管理", "支持复杂逻辑推理和决策分析", "支持架构图生成和可视化输出"]}}